import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/tasks'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/login/index.vue'),
    meta: {
      title: '登录',
      keepAlive: false
    }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/register/index.vue'),
    meta: {
      title: '注册',
      keepAlive: false
    }
  },
  {
    path: '/forgot-password',
    name: 'ForgotPassword',
    component: () => import('@/views/forgot-password/index.vue'),
    meta: {
      title: '找回密码',
      keepAlive: false
    }
  },
  {
    path: '/tasks',
    name: 'Tasks',
    component: () => import('@/views/tasks/index.vue'),
    meta: {
      title: '任务列表',
      keepAlive: true
    }
  },
  {
    path: '/my-tasks',
    name: 'MyTasks',
    component: () => import('@/views/my-tasks/index.vue'),
    meta: {
      title: '已接任务',
      keepAlive: true
    }
  },
  {
    path: '/task-detail/:id',
    name: 'TaskDetail',
    component: () => import('@/views/task-detail/index.vue'),
    meta: {
      title: '任务详情',
      keepAlive: false
    }
  },
  {
    path: '/user',
    name: 'User',
    component: () => import('@/views/user/index.vue'),
    meta: {
      title: '个人中心',
      keepAlive: true
    }
  },
  {
    path: '/accounts',
    name: 'Accounts',
    component: () => import('@/views/accounts/index.vue'),
    meta: {
      title: '账号库',
      keepAlive: false
    }
  },
  {
    path: '/accounts/add',
    name: 'AddAccount',
    component: () => import('@/views/accounts/add.vue'),
    meta: {
      title: '添加发布账号',
      keepAlive: false
    }
  },
  {
    path: '/submit-task/:id',
    name: 'SubmitTask',
    component: () => import('@/views/submit-task/index.vue'),
    meta: {
      title: '提交任务',
      keepAlive: false
    }
  },
  {
    path: '/bind-wechat',
    name: 'BindWechat',
    component: () => import('@/views/bind-wechat/index.vue'),
    meta: {
      title: '绑定微信',
      keepAlive: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由前置守卫
router.beforeEach((to, _from, next) => {
  // 设置页面标题
  document.title = to.meta.title as string || '小红书任务中心'

  // 判断是否需要登录权限
  const userStore = useUserStore()
  const isLogin = userStore.isLoggedIn

  // 不需要登录的页面
  const publicPages = ['/login', '/register', '/forgot-password']
  const isPublicPage = publicPages.includes(to.path)

  if (!isLogin && !isPublicPage) {
    // 未登录且访问需要登录的页面
    // 保存用户想要访问的目标URL（包含完整的路径和查询参数）
    const targetUrl = to.fullPath
    userStore.setRedirectUrl(targetUrl)

    // 跳转到登录页面
    next('/login')
  } else {
    // 已登录或访问公开页面，正常跳转
    next()
  }
})

export default router 